# Dify工作流配置说明 - 重点费用支出情况分析优化

## 概述

本文档说明如何在Dify工作流中配置优化后的重点费用支出情况分析提示词，以实现智能的执行状态判断。

## 主要改进

### 1. 后端数据处理优化

在`FinanceAnalysisServiceImpl.java`中，我们为以下项目添加了智能执行状态判断：

- 交易手续费
- 零售终端建设费  
- 信息系统维护费
- 党组织经费

### 2. 新增的数据字段

优化后的系统会在`calculatedValues`中提供以下新字段：

```json
{
  "交易手续费本年值": "40.74",
  "交易手续费预算值": "78.00", 
  "交易手续费执行进度": "52.24",
  "交易手续费执行状态": "执行进度过半",
  
  "零售终端建设费本年值": "0.00",
  "零售终端建设费预算值": "50.00",
  "零售终端建设费执行进度": "0.00", 
  "零售终端建设费执行状态": "尚未执行",
  
  "信息系统维护费本年值": "0.00",
  "信息系统维护费预算值": "30.00",
  "信息系统维护费执行进度": "0.00",
  "信息系统维护费执行状态": "尚未执行",
  
  "党组织经费本年值": "0.00", 
  "党组织经费预算值": "25.00",
  "党组织经费执行进度": "0.00",
  "党组织经费执行状态": "尚未执行"
}
```

## Dify工作流配置

### 1. 提示词模板更新

在Dify工作流的提示词节点中，使用以下优化后的模板：

```
基于<重点费用支出情况>，生成<result>内容，如下：

<result>
截至{{calculatedValues.截至日期}}，需要提醒各单位、各部门注意的事项金额变动较大支出项目情况

5、营销管理中心
（1）卷烟经营进销存方面：主营业务收入和成本执行进度约{{calculatedValues.主营业务执行进度}}%左右。

（2）零售终端建设费（重点费用）：预算{{calculatedValues.零售终端建设费预算值}}万元，{{calculatedValues.当前时期}}{% if calculatedValues.零售终端建设费执行状态 == "尚未执行" %}{{calculatedValues.零售终端建设费执行状态}}{% else %}实际执行{{calculatedValues.零售终端建设费本年值}}万元，执行进度{{calculatedValues.零售终端建设费执行进度}}%{% endif %}。

（3）交易手续费：年度预算{{calculatedValues.交易手续费预算值}}万元，{{calculatedValues.当前时期}}{% if calculatedValues.交易手续费执行状态 == "尚未执行" %}{{calculatedValues.交易手续费执行状态}}{% else %}实际执行{{calculatedValues.交易手续费本年值}}万元，执行进度{{calculatedValues.交易手续费执行进度}}%{% endif %}。

3、信息中心
信息系统维护费：预算{{calculatedValues.信息系统维护费预算值}}万元，{{calculatedValues.当前时期}}{% if calculatedValues.信息系统维护费执行状态 == "尚未执行" %}{{calculatedValues.信息系统维护费执行状态}}{% else %}实际执行{{calculatedValues.信息系统维护费本年值}}万元，执行进度{{calculatedValues.信息系统维护费执行进度}}%{% endif %}。

4、党建科
（2）党组织经费：预算{{calculatedValues.党组织经费预算值}}万元，{{calculatedValues.当前时期}}{% if calculatedValues.党组织经费执行状态 == "尚未执行" %}{{calculatedValues.党组织经费执行状态}}{% else %}实际执行{{calculatedValues.党组织经费本年值}}万元，执行进度{{calculatedValues.党组织经费执行进度}}%{% endif %}，主要是根据上年工资比例计提。
</result>
```

### 2. 条件判断逻辑

使用Jinja2模板语法实现条件判断：

```jinja2
{% if calculatedValues.交易手续费执行状态 == "尚未执行" %}
  {{calculatedValues.交易手续费执行状态}}
{% else %}
  实际执行{{calculatedValues.交易手续费本年值}}万元，执行进度{{calculatedValues.交易手续费执行进度}}%
{% endif %}
```

### 3. 执行状态类型

系统会返回以下执行状态类型：

- **"尚未执行"** - 当前值为0且执行进度为0
- **"已开始执行"** - 当前值>0但执行进度<50%
- **"执行进度过半"** - 执行进度≥50%且<100%
- **"已完成执行"** - 执行进度≥100%
- **"执行状态不明"** - 数据不完整

## 测试验证

### 1. 单元测试

运行以下测试验证逻辑正确性：

```bash
mvn test -Dtest=FinanceAnalysisServiceTest
```

### 2. 集成测试

1. 调用重点费用支出情况分析API
2. 检查返回的`calculatedValues`中是否包含执行状态字段
3. 验证Dify工作流输出是否正确显示执行情况

### 3. 示例验证

**输入数据**：
- 交易手续费预算：78万元
- 交易手续费实际执行：40.74万元
- 执行进度：52.24%

**期望输出**：
```
（3）交易手续费：年度预算78.00万元，1-6月实际执行40.74万元，执行进度52.24%。
```

**而不是**：
```
（3）交易手续费：年度预算78.00万元，1-6月尚未执行。
```

## 部署说明

1. 确保后端代码已更新并重新部署
2. 在Dify工作流中更新提示词模板
3. 测试各种执行状态的数据场景
4. 验证输出结果的准确性

## 注意事项

1. 确保数据库中的执行进度数据准确
2. 注意处理null值和零值的区别
3. 定期验证执行状态判断逻辑的准确性
4. 根据业务需求调整执行状态的阈值设置
