package org.springblade.modules.yjzb.service;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 财务分析服务测试类
 * 主要测试重点费用支出情况分析的执行状态判断逻辑
 */
@SpringBootTest
@ActiveProfiles("test")
public class FinanceAnalysisServiceTest {

    /**
     * 测试执行状态判断逻辑
     */
    @Test
    public void testExecutionStatusGeneration() {
        // 模拟测试数据
        Map<String, Object> testCases = new HashMap<>();
        
        // 测试用例1：尚未执行（当前值为0，执行进度为0）
        String status1 = generateExecutionStatusDescription(BigDecimal.ZERO, BigDecimal.ZERO, new BigDecimal("100"), "交易手续费");
        assertEquals("尚未执行", status1);
        
        // 测试用例2：尚未执行（当前值为null，执行进度为null）
        String status2 = generateExecutionStatusDescription(null, null, new BigDecimal("100"), "零售终端建设费");
        assertEquals("尚未执行", status2);
        
        // 测试用例3：已开始执行（当前值>0，执行进度<50%）
        String status3 = generateExecutionStatusDescription(new BigDecimal("20"), new BigDecimal("30"), new BigDecimal("100"), "信息系统维护费");
        assertEquals("已开始执行", status3);
        
        // 测试用例4：执行进度过半（执行进度≥50%且<100%）
        String status4 = generateExecutionStatusDescription(new BigDecimal("60"), new BigDecimal("60"), new BigDecimal("100"), "党组织经费");
        assertEquals("执行进度过半", status4);
        
        // 测试用例5：已完成执行（执行进度≥100%）
        String status5 = generateExecutionStatusDescription(new BigDecimal("100"), new BigDecimal("100"), new BigDecimal("100"), "包装费");
        assertEquals("已完成执行", status5);
        
        // 测试用例6：已开始执行（有当前值但无执行进度）
        String status6 = generateExecutionStatusDescription(new BigDecimal("30"), null, new BigDecimal("100"), "办公费");
        assertEquals("已开始执行", status6);
    }

    /**
     * 模拟generateExecutionStatusDescription方法的逻辑
     * 这里复制了实际方法的逻辑用于测试
     */
    private String generateExecutionStatusDescription(BigDecimal currentValue, BigDecimal executionRate, 
                                                    BigDecimal budgetValue, String itemName) {
        // 判断是否尚未执行的条件：
        // 1. 当前值为0或null
        // 2. 执行进度为0或null
        boolean isNotExecuted = (currentValue == null || currentValue.compareTo(BigDecimal.ZERO) == 0) 
                              && (executionRate == null || executionRate.compareTo(BigDecimal.ZERO) == 0);
        
        if (isNotExecuted) {
            return "尚未执行";
        } else {
            // 已执行，可以根据执行进度给出更详细的描述
            if (executionRate != null) {
                if (executionRate.compareTo(new BigDecimal("100")) >= 0) {
                    return "已完成执行";
                } else if (executionRate.compareTo(new BigDecimal("50")) >= 0) {
                    return "执行进度过半";
                } else {
                    return "已开始执行";
                }
            } else if (currentValue != null && currentValue.compareTo(BigDecimal.ZERO) > 0) {
                return "已开始执行";
            } else {
                return "执行状态不明";
            }
        }
    }

    /**
     * 测试实际场景的数据处理
     */
    @Test
    public void testRealScenarioData() {
        // 模拟实际的交易手续费数据
        Map<String, Object> transactionFeeData = new HashMap<>();
        transactionFeeData.put("2025年1-6月", new BigDecimal("40.74"));
        transactionFeeData.put("2025年预算数", new BigDecimal("78.00"));
        transactionFeeData.put("预算执行率%", new BigDecimal("52.24"));
        
        // 验证这种情况下应该显示"执行进度过半"而不是"尚未执行"
        String status = generateExecutionStatusDescription(
            new BigDecimal("40.74"), 
            new BigDecimal("52.24"), 
            new BigDecimal("78.00"), 
            "交易手续费"
        );
        assertEquals("执行进度过半", status);
        
        // 模拟零售终端建设费数据（真正尚未执行的情况）
        Map<String, Object> retailData = new HashMap<>();
        retailData.put("2025年1-6月", BigDecimal.ZERO);
        retailData.put("2025年预算数", new BigDecimal("50.00"));
        retailData.put("预算执行率%", BigDecimal.ZERO);
        
        String retailStatus = generateExecutionStatusDescription(
            BigDecimal.ZERO, 
            BigDecimal.ZERO, 
            new BigDecimal("50.00"), 
            "零售终端建设费"
        );
        assertEquals("尚未执行", retailStatus);
    }
}
